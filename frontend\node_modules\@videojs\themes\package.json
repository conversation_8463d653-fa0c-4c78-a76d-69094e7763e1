{"name": "@videojs/themes", "version": "1.0.1", "description": "", "main": "index.js", "scripts": {"build": "parcel build **/*.css index.html --no-source-maps --no-content-hash", "clean": "rm -rf ./dist", "dev": "parcel index.html", "prepublish": "npm run clean && npm run build"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"autoprefixer": "^9.6.1", "parcel-bundler": "^1.12.3", "postcss-modules": "^1.4.1", "postcss-preset-env": "^6.7.0", "sass": "^1.22.9"}, "dependencies": {"postcss-inline-svg": "^4.1.0"}}