{"name": "videojs-font", "version": "4.2.0", "description": "Video.js icon font", "repository": {"type": "git", "url": "https://github.com/videojs/font.git"}, "scripts": {"version": "grunt && git add ."}, "keywords": ["videojs"], "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/videojs/font/issues"}, "homepage": "https://github.com/videojs/font", "devDependencies": {"@babel/core": "^7.19.3", "@babel/preset-env": "^7.19.3", "@babel/register": "^7.18.9", "evil-icons": "^1.10.1", "grunt": "^1.5.3", "grunt-contrib-watch": "^1.1.0", "grunt-sass": "^3.1.0", "load-grunt-tasks": "^5.1.0", "material-design-icons": "^3.0.1", "node-sass": "^9.0.0", "time-grunt": "^2.0.0", "webfonts-generator": "^0.4.0"}}