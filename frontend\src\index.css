@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Video.js custom styles */
@import 'video.js/dist/video-js.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .input-field {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .word-highlight {
    @apply bg-yellow-200 text-yellow-900 px-1 rounded;
  }

  .word-highlight-active {
    @apply bg-yellow-400 text-yellow-900 px-1 rounded font-semibold;
  }
}

/* Video player container */
.video-player-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-player-container .video-js {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Custom subtitle styling */
.vjs-text-track-display {
  pointer-events: none;
}

.vjs-text-track-cue {
  background-color: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  font-size: 1.2em !important;
  line-height: 1.4 !important;
  padding: 0.2em 0.5em !important;
  border-radius: 0.25rem !important;
}

/* ESL controls styling */
.esl-controls {
  @apply flex flex-wrap gap-2 mt-4 p-4 bg-gray-50 rounded-lg;
}

.esl-controls button {
  @apply px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
}

.esl-controls button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Transcript panel */
.transcript-panel {
  @apply h-96 overflow-y-auto bg-gray-50 rounded-lg p-4;
}

.transcript-segment {
  @apply p-2 mb-2 rounded cursor-pointer transition-colors duration-200;
}

.transcript-segment:hover {
  @apply bg-gray-100;
}

.transcript-segment.active {
  @apply bg-primary-100 border-l-4 border-primary-500;
}

/* Upload progress */
.upload-progress {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.upload-progress-bar {
  @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
}
