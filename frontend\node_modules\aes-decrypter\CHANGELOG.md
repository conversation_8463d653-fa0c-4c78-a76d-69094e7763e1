<a name="4.0.2"></a>
## [4.0.2](https://github.com/videojs/aes-decrypter/compare/v4.0.0...v4.0.2) (2024-08-22)

### Chores

* do not run es-check on publish ([#87](https://github.com/videojs/aes-decrypter/issues/87)) ([6f0cbd9](https://github.com/videojs/aes-decrypter/commit/6f0cbd9))
* update vhs-utils dependency ([#88](https://github.com/videojs/aes-decrypter/issues/88)) ([d450547](https://github.com/videojs/aes-decrypter/commit/d450547))

<a name="4.0.0"></a>
# [4.0.0](https://github.com/videojs/aes-decrypter/compare/v3.1.3...v4.0.0) (2022-08-18)

### Chores

* **package:** remove IE11 support ([#86](https://github.com/videojs/aes-decrypter/issues/86)) ([3338e9b](https://github.com/videojs/aes-decrypter/commit/3338e9b))


### BREAKING CHANGES

* **package:** Internet Explorer is no longer supported.

<a name="3.1.3"></a>
## [3.1.3](https://github.com/videojs/aes-decrypter/compare/v3.1.2...v3.1.3) (2022-04-05)

### Bug Fixes

* update vhs-utils to 3.0.5 for tizen 2.4 support ([#85](https://github.com/videojs/aes-decrypter/issues/85)) ([1ab933b](https://github.com/videojs/aes-decrypter/commit/1ab933b))

<a name="3.1.2"></a>
## [3.1.2](https://github.com/videojs/aes-decrypter/compare/v3.1.1...v3.1.2) (2021-01-12)

### Bug Fixes

* cjs dist should only import cjs ([#83](https://github.com/videojs/aes-decrypter/issues/83)) ([a8a5fbf](https://github.com/videojs/aes-decrypter/commit/a8a5fbf))

<a name="3.1.1"></a>
## [3.1.1](https://github.com/videojs/aes-decrypter/compare/v3.1.0...v3.1.1) (2021-01-11)

### Chores

* update to use vhs-utils 3 ([#81](https://github.com/videojs/aes-decrypter/issues/81)) ([8ead5d9](https://github.com/videojs/aes-decrypter/commit/8ead5d9))

<a name="3.1.0"></a>
# [3.1.0](https://github.com/videojs/aes-decrypter/compare/v3.0.2...v3.1.0) (2020-11-03)

### Chores

* **package:** update to vhs-utils[@2](https://github.com/2) ([#80](https://github.com/videojs/aes-decrypter/issues/80)) ([63b9cb9](https://github.com/videojs/aes-decrypter/commit/63b9cb9))

<a name="3.0.2"></a>
## [3.0.2](https://github.com/videojs/aes-decrypter/compare/v3.0.1...v3.0.2) (2020-09-09)

### Chores

* **package:** update pkcs7 to remove engine check ([062c952](https://github.com/videojs/aes-decrypter/commit/062c952))

<a name="3.0.1"></a>
## [3.0.1](https://github.com/videojs/aes-decrypter/compare/v3.0.0...v3.0.1) (2019-08-21)

### Chores

* **package:** update rollup to version 0.66.0 ([#38](https://github.com/videojs/aes-decrypter/issues/38)) ([634556b](https://github.com/videojs/aes-decrypter/commit/634556b))
* bump videojs-generate-karma-config version ([#51](https://github.com/videojs/aes-decrypter/issues/51)) ([195b923](https://github.com/videojs/aes-decrypter/commit/195b923))
* **package:** update videojs-generate-karma-config to version 5.0.2 ([#57](https://github.com/videojs/aes-decrypter/issues/57)) ([be8bd81](https://github.com/videojs/aes-decrypter/commit/be8bd81))
* update generator version and use [@videojs](https://github.com/videojs)/vhs-utils ([#68](https://github.com/videojs/aes-decrypter/issues/68)) ([9a6ab2f](https://github.com/videojs/aes-decrypter/commit/9a6ab2f))
* Update to generator v7 standards ([#37](https://github.com/videojs/aes-decrypter/issues/37)) ([fcf96c4](https://github.com/videojs/aes-decrypter/commit/fcf96c4))
* Update videojs-generate-karma-config to the latest version 🚀 ([#42](https://github.com/videojs/aes-decrypter/issues/42)) ([2b16de3](https://github.com/videojs/aes-decrypter/commit/2b16de3))
* Update videojs-generate-karma-config to the latest version 🚀 ([#43](https://github.com/videojs/aes-decrypter/issues/43)) ([cb63ccd](https://github.com/videojs/aes-decrypter/commit/cb63ccd))

<a name="3.0.0"></a>
# [3.0.0](https://github.com/videojs/aes-decrypter/compare/v2.0.0...v3.0.0) (2017-07-24)

### Features

* Use Rollup for packaging ([bda57ab](https://github.com/videojs/aes-decrypter/commit/bda57ab))

### Chores

* prepare CHANGELOG for new process ([1a5175c](https://github.com/videojs/aes-decrypter/commit/1a5175c))


### BREAKING CHANGES

* revert to 1.x and stop using web crypto.

## 2.0.0 (2016-11-15)
* Use webcrypto for aes-cbc segment decryption when supported (#4)
* Lock the linter to a specific version

## 1.1.1 (2016-11-17)
* version to revert 1.1.0

## 1.0.3 (2016-06-16)
* dont do browserify-shim globally since we only use it in tests (#1)

## 1.0.2 (2016-06-16)
* specify browserify transform globally

## 1.0.1 (2016-06-16)
* fixing the build pipeline

## 1.0.0 (2016-06-16)
* initial

