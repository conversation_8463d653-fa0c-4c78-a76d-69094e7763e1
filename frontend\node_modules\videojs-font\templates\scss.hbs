// https://github.com/sass/sass/issues/659#issuecomment-64819075
@function char($character-code) {
  @if function-exists("selector-append") {
    @return unquote("\"\\#{$character-code}\"");
  }

  @return str-slice("\x", 1, 1) + $character-code;
}

$icon-font-family: {{fontName}};

@font-face {
  font-family: $icon-font-family;
  src: url(data:application/font-woff;charset=utf-8;base64,BASE64_WOFF_FONT) format('woff');
  font-weight: normal;
  font-style: normal;
}

// https://sass-lang.com/documentation/values/maps
$icons: (
  {{#each codepoints}}
    {{@key}}: '{{this}}',
  {{/each}}
);

// NOTE: This is as complex as we want to get with SCSS functionality.
//
// Now that we have a map of icons above, we can iterate over that map and create an icon class
// for each icon in that list. The iterator below produces CSS classes like this:
//
// .vjs-icon-play {
//   font-family: VideoJS;
//   font-weight: normal;
//   font-style: normal;
// }
// .vjs-icon-play:before { content: "\f101"; }
//
// We can then use @extend in the codebase when we need to add an icon to a class. @extend builds up
// the selectors for you so you can avoid duplication. This is generally a bad idea, but since each
// icon should only be extended one or two other places, we'll roll with it.
@each $name, $content in $icons {
  .vjs-icon-#{$name} {
    font-family: $icon-font-family;
    font-weight: normal;
    font-style: normal;

    &:before {
      content: char($content);
    }
  }
}
