module.exports = {
  allowCache: true,
  dateTimeObject: new Date('2019-02-14T02:13:36.106Z'),
  dateTimeString: '2019-02-14T02:13:36.106Z',
  dateRanges: [],
  discontinuitySequence: 0,
  discontinuityStarts: [],
  iFramePlaylists: [],
  mediaSequence: 266,
  preloadSegment: {
    map: {uri: 'init.mp4'},
    parts: [
      {
        duration: 0.33334,
        independent: true,
        uri: 'filePart273.0.mp4'
      },
      {
        duration: 0.33334,
        uri: 'filePart273.1.mp4'
      },
      {
        duration: 0.33334,
        uri: 'filePart273.2.mp4'
      }
    ],
    preloadHints: [
      {type: 'PART', uri: 'filePart273.3.mp4'},
      {type: 'MAP', uri: 'file-init.mp4'}
    ],
    timeline: 0
  },
  renditionReports: [
    {lastMsn: 273, lastPart: 2, uri: '../1M/waitForMSN.php'},
    {lastMsn: 273, lastPart: 1, uri: '../4M/waitForMSN.php'}
  ],
  partInf: {
    partTarget: 0.33334
  },
  partTargetDuration: 0.33334,
  segments: [
    {
      dateTimeObject: new Date('2019-02-14T02:13:36.106Z'),
      dateTimeString: '2019-02-14T02:13:36.106Z',
      programDateTime: 1550110416106,
      duration: 4.00008,
      map: {
        uri: 'init.mp4'
      },
      timeline: 0,
      uri: 'fileSequence266.mp4'
    },
    {
      duration: 4.00008,
      map: {
        uri: 'init.mp4'
      },
      programDateTime: 1550110420106.08,
      timeline: 0,
      uri: 'fileSequence267.mp4'
    },
    {
      duration: 4.00008,
      map: {
        uri: 'init.mp4'
      },
      programDateTime: 1550110424106.1602,
      timeline: 0,
      uri: 'fileSequence268.mp4'
    },
    {
      duration: 4.00008,
      map: {
        uri: 'init.mp4'
      },
      programDateTime: 1550110428106.2402,
      timeline: 0,
      uri: 'fileSequence269.mp4'
    },
    {
      duration: 4.00008,
      map: {
        uri: 'init.mp4'
      },
      programDateTime: 1550110432106.3203,
      timeline: 0,
      uri: 'fileSequence270.mp4'
    },
    {
      duration: 4.00008,
      map: {
        uri: 'init.mp4'
      },
      programDateTime: 1550110436106.4004,
      timeline: 0,
      uri: 'fileSequence271.mp4',
      parts: [
        {
          duration: 0.33334,
          uri: 'filePart271.0.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart271.1.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart271.2.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart271.3.mp4'
        },
        {
          duration: 0.33334,
          independent: true,
          uri: 'filePart271.4.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart271.5.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart271.6.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart271.7.mp4'
        },
        {
          duration: 0.33334,
          independent: true,
          uri: 'filePart271.8.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart271.9.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart271.10.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart271.11.mp4'
        }
      ]
    },
    {
      dateTimeObject: new Date('2019-02-14T02:14:00.106Z'),
      dateTimeString: '2019-02-14T02:14:00.106Z',
      duration: 4.00008,
      map: {
        uri: 'init.mp4'
      },
      programDateTime: 1550110440106,
      timeline: 0,
      uri: 'fileSequence272.mp4',
      parts: [
        {
          duration: 0.33334,
          gap: true,
          uri: 'filePart272.a.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.b.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.c.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.d.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.e.mp4'
        },
        {
          duration: 0.33334,
          independent: true,
          uri: 'filePart272.f.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.g.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.h.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.i.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.j.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.k.mp4'
        },
        {
          duration: 0.33334,
          uri: 'filePart272.l.mp4'
        }
      ]
    }
  ],
  serverControl: {
    canSkipDateranges: true,
    canBlockReload: true,
    canSkipUntil: 12,
    partHoldBack: 1,
    holdBack: 12
  },
  targetDuration: 4,
  version: 6
};
