{"name": "pkcs7", "version": "1.0.4", "main": "dist/pkcs7.cjs.js", "module": "dist/pkcs7.es.js", "description": "Add and remove pkcs7-style padding.", "homepage": "https://github.com/brightcove/pkcs7", "bugs": "https://github.com/brightcove/pkcs7/issues", "author": {"name": "Brightcove"}, "repository": {"type": "git", "url": "https://github.com/brightcove/pkcs7"}, "license": "Apache-2.0", "files": ["CONTRIBUTING.md", "dist/", "docs/", "index.html", "scripts/", "src/", "test/", "bin"], "keywords": ["pkcs7", "pkcs7 encrypt decrypt padding pkcs", "videojs", "videojs-plugin"], "dependencies": {"@babel/runtime": "^7.5.5"}, "devDependencies": {"rollup": "^1.19.4", "@videojs/generator-helpers": "~1.2.0", "karma": "^4.0.0", "sinon": "^7.2.2", "videojs-generate-karma-config": "~5.3.1", "videojs-generator-verify": "~2.0.0", "videojs-generate-rollup-config": "~5.0.0", "videojs-standard": "^8.0.3"}, "scripts": {"build-test": "cross-env-shell TEST_BUNDLE_ONLY=1 'npm run build'", "build-prod": "cross-env-shell NO_TEST_BUNDLE=1 'npm run build'", "build": "npm-run-all -s clean -p build:*", "build:js": "rollup -c scripts/rollup.config.js", "clean": "shx rm -rf ./dist ./test/dist && shx mkdir -p ./dist ./test/dist", "lint": "vjsstandard", "prepublishOnly": "npm-run-all build-prod && vjsverify --verbose", "start": "npm-run-all -p server watch", "server": "karma start scripts/karma.conf.js --singleRun=false --auto-watch", "test": "npm-run-all lint build-test -p test:*", "test:browser": "karma start scripts/karma.conf.js", "test:node": "qunit test/dist/bundle.js", "posttest": "shx cat test/dist/coverage/text.txt", "preversion": "npm test", "version": "is-prerelease || npm run update-changelog && git add CHANGELOG.md", "update-changelog": "conventional-changelog -p videojs -i CHANGELOG.md -s", "watch": "npm-run-all -p watch:*", "watch:js": "npm run build:js -- -w"}, "bin": {"pkcs7": "bin/cli.js"}, "vjsstandard": {"ignore": ["dist", "docs", "test/dist"]}, "generator-videojs-plugin": {"version": "7.7.3"}, "browserslist": ["defaults", "ie 11"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["vjsstandard --fix", "git add"], "README.md": ["doctoc --notitle", "git add"]}}