module.exports = {
  allowCache: true,
  dateRanges: [],
  discontinuitySequence: 0,
  discontinuityStarts: [],
  iFramePlaylists: [],
  mediaSequence: 0,
  playlistType: 'VOD',
  preloadSegment: {
    parts: [
      {
        duration: 0.33334,
        uri: 'hls_450k_video.ts',
        byterange: {
          length: 22444,
          offset: 0
        }
      }
    ],
    preloadHints: [
      {
        type: 'PART',
        uri: 'filePart273.1.mp4',
        byterange: {
          length: 2000,
          offset: 22444
        }
      },
      {
        type: 'MAP',
        uri: 'file-init.mp4',
        byterange: {
          length: 5000,
          offset: 8377660
        }
      },
      {
        type: 'FOO',
        uri: 'foo.mp4',
        byterange: {
          length: 5000,
          offset: 0
        }
      }
    ],
    timeline: 0
  },
  segments: [
    {
      byterange: {
        length: 468684,
        offset: 7108092
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 444996,
        offset: 7576776
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 331444,
        offset: 8021772
      },
      duration: 10,
      parts: [
        {
          duration: 0.33334,
          uri: 'hls_450k_video.ts',
          byterange: {
            length: 45553,
            offset: 0
          }
        },
        {
          duration: 0.33334,
          uri: 'hls_450k_video.ts',
          byterange: {
            length: 28823,
            offset: 7622329
          }
        },
        {
          duration: 0.33334,
          uri: 'hls_450k_video.ts',
          byterange: {
            length: 22444,
            offset: 7651152
          }
        },
        {
          duration: 0.33334,
          uri: 'hls_450k_video.ts',
          byterange: {
            length: 22444,
            offset: 7673596
          }
        }
      ],
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 44556,
        offset: 8353216
      },
      duration: 1.4167,
      parts: [
        {
          duration: 0.33334,
          uri: 'hls_450k_video.ts',
          byterange: {
            length: 45553,
            offset: 8021772
          }
        },
        {
          duration: 0.33334,
          uri: 'hls_450k_video.ts',
          byterange: {
            length: 28823,
            offset: 8067325
          }
        },
        {
          duration: 0.33334,
          uri: 'hls_450k_video.ts',
          byterange: {
            length: 22444,
            offset: 8096148
          }
        }
      ],
      timeline: 0,
      uri: 'hls_450k_video.ts'
    }
  ],
  skip: {
    skippedSegments: 3
  },
  targetDuration: 10,
  version: 3
};
