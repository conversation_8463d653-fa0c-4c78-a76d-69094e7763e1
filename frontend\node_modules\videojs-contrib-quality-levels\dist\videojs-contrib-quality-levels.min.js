/*! @name videojs-contrib-quality-levels @version 4.1.0 @license Apache-2.0 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("video.js")):"function"==typeof define&&define.amd?define(["video.js"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).videojsContribQualityLevels=t(e.videojs)}(this,(function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var l=t(e);class i{constructor(e){let t=this;return t.id=e.id,t.label=t.id,t.width=e.width,t.height=e.height,t.bitrate=e.bandwidth,t.frameRate=e.frameRate,t.enabled_=e.enabled,Object.defineProperty(t,"enabled",{get:()=>t.enabled_(),set(e){t.enabled_(e)}}),t}}class s extends l.default.EventTarget{constructor(){super();let e=this;return e.levels_=[],e.selectedIndex_=-1,Object.defineProperty(e,"selectedIndex",{get:()=>e.selectedIndex_}),Object.defineProperty(e,"length",{get:()=>e.levels_.length}),e[Symbol.iterator]=()=>e.levels_.values(),e}addQualityLevel(e){let t=this.getQualityLevelById(e.id);if(t)return t;const l=this.levels_.length;return t=new i(e),""+l in this||Object.defineProperty(this,l,{get(){return this.levels_[l]}}),this.levels_.push(t),this.trigger({qualityLevel:t,type:"addqualitylevel"}),t}removeQualityLevel(e){let t=null;for(let l=0,i=this.length;l<i;l++)if(this[l]===e){t=this.levels_.splice(l,1)[0],this.selectedIndex_===l?this.selectedIndex_=-1:this.selectedIndex_>l&&this.selectedIndex_--;break}return t&&this.trigger({qualityLevel:e,type:"removequalitylevel"}),t}getQualityLevelById(e){for(let t=0,l=this.length;t<l;t++){const l=this[t];if(l.id===e)return l}return null}dispose(){this.selectedIndex_=-1,this.levels_.length=0}}s.prototype.allowedEvents_={change:"change",addqualitylevel:"addqualitylevel",removequalitylevel:"removequalitylevel"};for(const e in s.prototype.allowedEvents_)s.prototype["on"+e]=null;var n="4.1.0";const d=function(e){return function(e,t){const l=e.qualityLevels,i=new s,d=function(){i.dispose(),e.qualityLevels=l,e.off("dispose",d)};return e.on("dispose",d),e.qualityLevels=()=>i,e.qualityLevels.VERSION=n,i}(this,l.default.obj.merge({},e))};return l.default.registerPlugin("qualityLevels",d),d.VERSION=n,d}));
