{"name": "videojs-contrib-quality-levels", "version": "4.1.0", "description": "Exposes a list of quality levels available for the source.", "main": "dist/videojs-contrib-quality-levels.cjs.js", "types": "dist/types/plugin.d.ts", "jsnext:main": "src/plugin.js", "generator-videojs-plugin": {"version": "8.0.0"}, "repository": "videojs/videojs-contrib-quality-levels", "scripts": {"prebuild": "npm run clean", "build": "npm-run-all -s clean -p build:*", "build-prod": "cross-env-shell NO_TEST_BUNDLE=1 'npm run build'", "build-test": "cross-env-shell TEST_BUNDLE_ONLY=1 'npm run build'", "build:js": "rollup -c scripts/rollup.config.js", "build:types": "tsc", "clean": "shx rm -rf ./dist ./test/dist ./cjs ./es && shx mkdir -p ./dist ./test/dist ./cjs ./es", "postclean": "shx mkdir -p ./dist ./test/dist", "docs": "npm-run-all docs:*", "docs:api": "jsdoc src -c scripts/jsdoc.config.json -r -d docs/api", "docs:toc": "doctoc --notitle README.md", "lint": "vjsstandard", "server": "karma start scripts/karma.conf.js --singleRun=false --auto-watch", "start": "npm-run-all -p server watch", "pretest": "npm-run-all lint build", "test": "npm-run-all lint build-test && karma start scripts/karma.conf.js", "posttest": "shx cat test/dist/coverage/text.txt", "update-changelog": "conventional-changelog -p videojs -i CHANGELOG.md -s", "preversion": "npm test", "version": "is-prerelease || npm run update-changelog && git add CHANGELOG.md", "watch": "npm-run-all -p watch:*", "watch:js": "npm run build:js -- -w", "prepublishOnly": "npm-run-all build-prod && vjsverify --verbose --skip-es-check", "prepare": "husky install"}, "keywords": ["videojs", "videojs-plugin"], "author": "Brightcove, Inc.", "license": "Apache-2.0", "copyright": "Copyright Brightcove, Inc. <https://www.brightcove.com/>", "videojs-plugin": {"script": "dist/videojs-contrib-quality-levels.min.js"}, "vjsstandard": {"ignore": ["cjs", "dist", "docs", "ejs", "test/dist"]}, "files": ["CONTRIBUTING.md", "dist/", "docs/", "index.html", "scripts/", "src/", "test/"], "dependencies": {"global": "^4.4.0"}, "peerDependencies": {"video.js": "^8"}, "devDependencies": {"@babel/cli": "^7.13.16", "@babel/runtime": "^7.14.0", "@videojs/babel-config": "^0.2.0", "@videojs/generator-helpers": "~2.0.2", "conventional-changelog-cli": "^2.0.1", "conventional-changelog-videojs": "^3.0.0", "doctoc": "^1.3.1", "husky": "^8.0.3", "jsdoc": "^3.6.11", "karma": "^6.3.2", "lint-staged": "^7.2.2", "not-prerelease": "^1.0.1", "npm-merge-driver-install": "^1.0.0", "npm-run-all": "^4.1.5", "pkg-ok": "^2.2.0", "rollup": "^2.46.0", "semver": "^5.1.0", "shx": "^0.3.2", "sinon": "^9.1.0", "typescript": "^5.4.2", "video.js": "^8", "videojs-generate-karma-config": "~8.0.0", "videojs-generate-rollup-config": "^7.0.0", "videojs-generator-verify": "^4.1.0", "videojs-standard": "^9.0.1"}, "module": "dist/videojs-contrib-quality-levels.es.js", "lint-staged": {"*.js": "vjsstandard --fix", "README.md": "doctoc --notitle"}, "browser": "dist/videojs-contrib-quality-levels.js", "engines": {"node": ">=16", "npm": ">=8"}}