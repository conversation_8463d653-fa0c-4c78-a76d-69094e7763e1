<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>VideoJS</title>
  <link href="css/videojs-icons.css" rel="stylesheet">
  <style>
    body {
      text-align: center;
    }

    div {
      display: inline-block;
      border: 1px solid #ccc;
      text-align: center;
      width: 150px;
      margin: 10px;
      padding: 10px;
    }

    div span:first-of-type {
      font-size: 200%;
    }

    div span:first-of-type::before {
      display: inline-block;
      width: 100%;
    }
  </style>
</head>
<body>
  <h1>VideoJS Icons</h1>
  <p>All icons prefixed by <code>vjs-icon-</code></p>
  <div>
    <span class="vjs-icon-play"></span>
    <span>play</span>
  </div>
  <div>
    <span class="vjs-icon-play-circle"></span>
    <span>play-circle</span>
  </div>
  <div>
    <span class="vjs-icon-pause"></span>
    <span>pause</span>
  </div>
  <div>
    <span class="vjs-icon-volume-mute"></span>
    <span>volume-mute</span>
  </div>
  <div>
    <span class="vjs-icon-volume-low"></span>
    <span>volume-low</span>
  </div>
  <div>
    <span class="vjs-icon-volume-mid"></span>
    <span>volume-mid</span>
  </div>
  <div>
    <span class="vjs-icon-volume-high"></span>
    <span>volume-high</span>
  </div>
  <div>
    <span class="vjs-icon-fullscreen-enter"></span>
    <span>fullscreen-enter</span>
  </div>
  <div>
    <span class="vjs-icon-fullscreen-exit"></span>
    <span>fullscreen-exit</span>
  </div>
  <div>
    <span class="vjs-icon-spinner"></span>
    <span>spinner</span>
  </div>
  <div>
    <span class="vjs-icon-subtitles"></span>
    <span>subtitles</span>
  </div>
  <div>
    <span class="vjs-icon-captions"></span>
    <span>captions</span>
  </div>
  <div>
    <span class="vjs-icon-hd"></span>
    <span>hd</span>
  </div>
  <div>
    <span class="vjs-icon-chapters"></span>
    <span>chapters</span>
  </div>
  <div>
    <span class="vjs-icon-downloading"></span>
    <span>downloading</span>
  </div>
  <div>
    <span class="vjs-icon-file-download"></span>
    <span>file-download</span>
  </div>
  <div>
    <span class="vjs-icon-file-download-done"></span>
    <span>file-download-done</span>
  </div>
  <div>
    <span class="vjs-icon-file-download-off"></span>
    <span>file-download-off</span>
  </div>
  <div>
    <span class="vjs-icon-share"></span>
    <span>share</span>
  </div>
  <div>
    <span class="vjs-icon-cog"></span>
    <span>cog</span>
  </div>
  <div>
    <span class="vjs-icon-square"></span>
    <span>square</span>
  </div>
  <div>
    <span class="vjs-icon-circle"></span>
    <span>circle</span>
  </div>
  <div>
    <span class="vjs-icon-circle-outline"></span>
    <span>circle-outline</span>
  </div>
  <div>
    <span class="vjs-icon-circle-inner-circle"></span>
    <span>circle-inner-circle</span>
  </div>
  <div>
    <span class="vjs-icon-cancel"></span>
    <span>cancel</span>
  </div>
  <div>
    <span class="vjs-icon-repeat"></span>
    <span>repeat</span>
  </div>
  <div>
    <span class="vjs-icon-replay"></span>
    <span>replay</span>
  </div>
  <div>
    <span class="vjs-icon-replay-5"></span>
    <span>replay-5</span>
  </div>
  <div>
    <span class="vjs-icon-replay-10"></span>
    <span>replay-10</span>
  </div>
  <div>
    <span class="vjs-icon-replay-30"></span>
    <span>replay-30</span>
  </div>
  <div>
    <span class="vjs-icon-forward-5"></span>
    <span>forward-5</span>
  </div>
  <div>
    <span class="vjs-icon-forward-10"></span>
    <span>forward-10</span>
  </div>
  <div>
    <span class="vjs-icon-forward-30"></span>
    <span>forward-30</span>
  </div>
  <div>
    <span class="vjs-icon-audio"></span>
    <span>audio</span>
  </div>
  <div>
    <span class="vjs-icon-next-item"></span>
    <span>next-item</span>
  </div>
  <div>
    <span class="vjs-icon-previous-item"></span>
    <span>previous-item</span>
  </div>
  <div>
    <span class="vjs-icon-shuffle"></span>
    <span>shuffle</span>
  </div>
  <div>
    <span class="vjs-icon-cast"></span>
    <span>cast</span>
  </div>
  <div>
    <span class="vjs-icon-picture-in-picture-enter"></span>
    <span>picture-in-picture-enter</span>
  </div>
  <div>
    <span class="vjs-icon-picture-in-picture-exit"></span>
    <span>picture-in-picture-exit</span>
  </div>
  <div>
    <span class="vjs-icon-facebook"></span>
    <span>facebook</span>
  </div>
  <div>
    <span class="vjs-icon-linkedin"></span>
    <span>linkedin</span>
  </div>
  <div>
    <span class="vjs-icon-twitter"></span>
    <span>twitter</span>
  </div>
  <div>
    <span class="vjs-icon-tumblr"></span>
    <span>tumblr</span>
  </div>
  <div>
    <span class="vjs-icon-pinterest"></span>
    <span>pinterest</span>
  </div>
  <div>
    <span class="vjs-icon-audio-description"></span>
    <span>audio-description</span>
  </div>
</body>
</html>
