<a name="1.0.4"></a>
## [1.0.4](https://github.com/brightcove/pkcs7/compare/v1.0.3...v1.0.4) (2020-09-09)

### Chores

* **package:** remove postcss-cli ([aed7b3a](https://github.com/brightcove/pkcs7/commit/aed7b3a))

<a name="1.0.3"></a>
## [1.0.3](https://github.com/brightcove/pkcs7/compare/v1.0.2...v1.0.3) (2019-08-21)

### Chores

* generator 7.7.3 update ([#13](https://github.com/brightcove/pkcs7/issues/13)) ([da31c29](https://github.com/brightcove/pkcs7/commit/da31c29))
* remove pkg.engines field ([#9](https://github.com/brightcove/pkcs7/issues/9)) ([17a1d1d](https://github.com/brightcove/pkcs7/commit/17a1d1d))
* update npm-run-all to fix security issue ([#11](https://github.com/brightcove/pkcs7/issues/11)) ([a07d6d7](https://github.com/brightcove/pkcs7/commit/a07d6d7))
* Update to generator v7 standards ([#10](https://github.com/brightcove/pkcs7/issues/10)) ([e075fe6](https://github.com/brightcove/pkcs7/commit/e075fe6))
* update videojs-generator-verify to fix security issue ([c1e6bd7](https://github.com/brightcove/pkcs7/commit/c1e6bd7))

