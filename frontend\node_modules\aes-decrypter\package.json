{"name": "aes-decrypter", "version": "4.0.2", "description": "decrypt aes-128 content using a key", "main": "dist/aes-decrypter.cjs.js", "module": "dist/aes-decrypter.es.js", "contributors": ["gkatsev", "imbcmdth", "dmlap", "bcasey"], "scripts": {"build-test": "cross-env-shell TEST_BUNDLE_ONLY=1 'npm run build'", "build-prod": "cross-env-shell NO_TEST_BUNDLE=1 'npm run build'", "build": "npm-run-all -s clean -p build:*", "build:js": "rollup -c scripts/rollup.config.js", "clean": "shx rm -rf ./dist ./test/dist && shx mkdir -p ./dist ./test/dist", "lint": "vjsstandard", "prepublishOnly": "npm-run-all build-prod && vjsverify --verbose --skip-es-check", "start": "npm-run-all -p server watch", "server": "karma start scripts/karma.conf.js --singleRun=false --auto-watch", "test": "npm-run-all lint build-test && karma start scripts/karma.conf.js", "posttest": "shx cat test/dist/coverage/text.txt", "preversion": "npm test", "version": "is-prerelease || npm run update-changelog && git add CHANGELOG.md", "update-changelog": "conventional-changelog -p videojs -i CHANGELOG.md -s", "watch": "npm-run-all -p watch:*", "watch:js": "npm run build:js -- -w"}, "author": "Brightcove, Inc.", "license": "Apache-2.0", "vjsstandard": {"ignore": ["dist", "docs", "test/dist"]}, "files": ["CONTRIBUTING.md", "dist/", "docs/", "index.html", "scripts/", "src/", "test/"], "dependencies": {"@babel/runtime": "^7.12.5", "@videojs/vhs-utils": "^4.1.1", "global": "^4.4.0", "pkcs7": "^1.0.4"}, "devDependencies": {"@rollup/plugin-replace": "^2.3.4", "@videojs/generator-helpers": "~2.0.1", "karma": "^5.2.3", "rollup": "^2.38.0", "sinon": "^9.2.3", "videojs-generate-karma-config": "^8.0.1", "videojs-generate-rollup-config": "~7.0.0", "videojs-generator-verify": "~3.0.1", "videojs-standard": "^8.0.4"}, "generator-videojs-plugin": {"version": "7.7.3"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git+https://github.com/videojs/aes-decrypter.git"}, "bugs": {"url": "https://github.com/videojs/aes-decrypter/issues"}, "homepage": "https://github.com/videojs/aes-decrypter#readme", "keywords": ["videojs", "videojs-plugin"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": "vjsstandard --fix", "README.md": "doctoc --notitle"}}