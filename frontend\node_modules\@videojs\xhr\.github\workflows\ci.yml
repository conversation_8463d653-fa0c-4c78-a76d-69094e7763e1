name: ci

on: [push, pull_request]

jobs:
  should-skip:
    continue-on-error: true
    runs-on: ubuntu-latest
    # Map a step output to a job output
    outputs:
      should-skip-job: ${{steps.skip-check.outputs.should_skip}}
    steps:
      - id: skip-check
        uses: fkirc/skip-duplicate-actions@v2.1.0
        with:
          github_token: ${{github.token}}

  ci:
    needs: should-skip
    if: ${{needs.should-skip.outputs.should-skip-job != 'true' || github.ref == 'refs/heads/main'}}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
    env:
      BROWSER_STACK_USERNAME: ${{secrets.BROWSER_STACK_USERNAME}}
      BROWSER_STACK_ACCESS_KEY: ${{secrets.BROWSER_STACK_ACCESS_KEY}}
    runs-on: ${{matrix.os}}
    steps:
    - name: checkout code
      uses: actions/checkout@v2

    - name: read node version from .nvmrc
      run: echo ::set-output name=NVMRC::$(cat .nvmrc)
      shell: bash
      id: nvm

    - name: update apt cache on linux w/o browserstack
      run: sudo apt-get update

    - name: install ffmpeg/pulseaudio for firefox on linux w/o browserstack
      run: sudo apt-get install ffmpeg pulseaudio

    - name: start pulseaudio for firefox on linux w/o browserstack
      run: pulseaudio -D

    - name: setup node
      uses: actions/setup-node@v2
      with:
        node-version: '${{steps.nvm.outputs.NVMRC}}'
        cache: npm

    # turn off the default setup-node problem watchers...
    - run: echo "::remove-matcher owner=eslint-compact::"
    - run: echo "::remove-matcher owner=eslint-stylish::"
    - run: echo "::remove-matcher owner=tsc::"

    - name: npm install
      run: npm i --prefer-offline --no-audit

    - name: run npm test
      uses: GabrielBB/xvfb-action@v1
      with:
        run: npm run test
